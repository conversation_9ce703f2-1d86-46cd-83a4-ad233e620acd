#!/bin/bash

# H.265 2-Pass VBR高效视频压缩脚本 (四层架构优化版)
# 使用H.265编码器，2-Pass VBR，目标80MB，无音频处理，智能压缩策略
# 大于90MB的视频压缩至80MB，小于90MB的视频直接复制

echo "🎬 开始H.265 2-Pass VBR高效视频压缩 (四层架构优化)..."

# 检查ffmpeg H.265支持
if ! ffmpeg -encoders 2>/dev/null | grep -q libx265; then
    echo "❌ 错误: FFmpeg不支持libx265编码器"
    echo "请安装支持H.265的FFmpeg版本"
    exit 1
fi

# 创建输出目录 (统一路径)
mkdir -p src/client/assets/video-compressed

echo "📁 输出目录："
echo "  - 压缩视频存储: src/client/assets/video-compressed/"
echo "  - 统一路径管理，支持四层CDN架构"

# 2-Pass VBR智能压缩策略函数
compress_video() {
    local input_file="$1"
    local output_name="$2"
    local target_size_mb="$3"
    local description="$4"

    if [ ! -f "$input_file" ]; then
        echo "⚠️  跳过 $output_name: 源文件不存在 ($input_file)"
        return 1
    fi

    # 获取文件大小 (MB) - 使用awk替代bc提高兼容性
    local file_size=$(stat -c%s "$input_file" 2>/dev/null || echo "0")
    local file_size_mb=$(awk "BEGIN {printf \"%.1f\", $file_size/1024/1024}" 2>/dev/null || echo "0")

    echo "📹 处理 $output_name ($description)..."
    echo "   源文件大小: ${file_size_mb}MB"

    # 智能压缩策略：小于90MB的文件去除音频但不压缩
    if (( $(awk "BEGIN {print ($file_size_mb < 90)}") )); then
        echo "   📋 文件小于90MB，去除音频但不压缩画质"
        echo "   🔄 开始去除音频处理..."

        ffmpeg -i "$input_file" \
            -c:v copy \
            -an \
            -movflags +faststart \
            -y \
            "src/client/assets/video-compressed/$output_name"

        if [ $? -eq 0 ]; then
            # 获取处理后大小
            local processed_size=$(stat -c%s "src/client/assets/video-compressed/$output_name" 2>/dev/null || echo "0")
            local processed_mb=$(awk "BEGIN {printf \"%.1f\", $processed_size/1024/1024}" 2>/dev/null || echo "0")

            echo "   📊 音频去除完成: ${processed_mb}MB"
            echo "   ✅ 文件已保存到统一路径"
            return 0
        else
            echo "   ❌ 音频去除失败"
            return 1
        fi
    fi

    # 2-Pass VBR压缩
    echo "   🔄 开始H.265 2-Pass VBR压缩 (目标: ${target_size_mb}MB)..."

    # 计算目标码率 (kbps)
    # 获取视频时长
    local duration=$(ffprobe -v quiet -show_entries format=duration -of csv=p=0 "$input_file" 2>/dev/null || echo "0")
    if [ "$duration" = "0" ] || [ -z "$duration" ]; then
        echo "   ❌ 无法获取视频时长"
        return 1
    fi

    # 计算目标码率: (目标文件大小MB * 8 * 1024) / 时长秒 = kbps
    local target_bitrate=$(awk "BEGIN {printf \"%.0f\", ($target_size_mb * 8 * 1024) / $duration}")
    echo "   📊 计算参数: 时长=${duration}s, 目标码率=${target_bitrate}kbps"

    # 第一遍：分析
    echo "   🔍 第一遍: 分析视频..."
    ffmpeg -i "$input_file" \
        -c:v libx265 \
        -b:v "${target_bitrate}k" \
        -maxrate "$((target_bitrate * 12 / 10))k" \
        -bufsize "$((target_bitrate * 2))k" \
        -preset slow \
        -an \
        -pix_fmt yuv420p \
        -pass 1 \
        -f null \
        /dev/null \
        -y

    if [ $? -ne 0 ]; then
        echo "   ❌ 第一遍分析失败"
        return 1
    fi

    # 第二遍：编码
    echo "   🎬 第二遍: 精确编码..."
    ffmpeg -i "$input_file" \
        -c:v libx265 \
        -b:v "${target_bitrate}k" \
        -maxrate "$((target_bitrate * 12 / 10))k" \
        -bufsize "$((target_bitrate * 2))k" \
        -preset slow \
        -an \
        -pix_fmt yuv420p \
        -movflags +faststart \
        -tag:v hvc1 \
        -pass 2 \
        -y \
        "src/client/assets/video-compressed/$output_name"

    # 清理临时文件
    rm -f ffmpeg2pass-0.log ffmpeg2pass-0.log.mbtree 2>/dev/null

    if [ $? -eq 0 ]; then
        # 获取压缩后大小 - 使用awk替代bc
        local compressed_size=$(stat -c%s "src/client/assets/video-compressed/$output_name" 2>/dev/null || echo "0")
        local compressed_mb=$(awk "BEGIN {printf \"%.1f\", $compressed_size/1024/1024}" 2>/dev/null || echo "0")
        local compression_ratio=$(awk "BEGIN {printf \"%.1f\", (1 - $compressed_size / $file_size) * 100}" 2>/dev/null || echo "0")

        echo "   📊 2-Pass VBR压缩完成: ${compressed_mb}MB (压缩率: ${compression_ratio}%)"
        echo "   ✅ 文件已保存到统一路径"
        return 0
    else
        echo "   ❌ 第二遍编码失败"
        return 1
    fi
}

# 并行压缩配置
MAX_PARALLEL_JOBS=2  # 基于CPU核心数设置并行度
CURRENT_JOBS=0
declare -a JOB_PIDS=()
declare -a JOB_NAMES=()

# 并行任务管理函数
wait_for_job_slot() {
    while [ ${#JOB_PIDS[@]} -ge $MAX_PARALLEL_JOBS ]; do
        for i in "${!JOB_PIDS[@]}"; do
            if ! kill -0 "${JOB_PIDS[$i]}" 2>/dev/null; then
                echo "✅ 任务完成: ${JOB_NAMES[$i]}"
                unset JOB_PIDS[$i]
                unset JOB_NAMES[$i]
                # 重新索引数组
                JOB_PIDS=("${JOB_PIDS[@]}")
                JOB_NAMES=("${JOB_NAMES[@]}")
                break
            fi
        done
        sleep 2
    done
}

# 并行压缩函数
compress_video_parallel() {
    local input_file="$1"
    local output_name="$2"
    local target_size_mb="$3"
    local description="$4"

    wait_for_job_slot

    echo "🚀 启动并行任务: $description"

    # 在后台启动压缩任务
    (
        compress_video "$input_file" "$output_name" "$target_size_mb" "$description"
    ) &

    local job_pid=$!
    JOB_PIDS+=($job_pid)
    JOB_NAMES+=("$description")

    echo "📋 任务 PID: $job_pid | 当前并行任务数: ${#JOB_PIDS[@]}/$MAX_PARALLEL_JOBS"
}

# 等待所有任务完成
wait_for_all_jobs() {
    echo ""
    echo "⏳ 等待所有并行任务完成..."

    while [ ${#JOB_PIDS[@]} -gt 0 ]; do
        for i in "${!JOB_PIDS[@]}"; do
            if ! kill -0 "${JOB_PIDS[$i]}" 2>/dev/null; then
                echo "✅ 任务完成: ${JOB_NAMES[$i]}"
                unset JOB_PIDS[$i]
                unset JOB_NAMES[$i]
                # 重新索引数组
                JOB_PIDS=("${JOB_PIDS[@]}")
                JOB_NAMES=("${JOB_NAMES[@]}")
            fi
        done

        if [ ${#JOB_PIDS[@]} -gt 0 ]; then
            echo "🔄 剩余任务: ${#JOB_PIDS[@]} | 任务名称: ${JOB_NAMES[*]}"
            sleep 5
        fi
    done

    echo "🎉 所有并行任务已完成！"
}

# 压缩所有视频文件 (并行处理)
echo ""
echo "🚀 开始并行2-Pass VBR压缩 (最大并行度: $MAX_PARALLEL_JOBS)..."
echo "💻 系统CPU核心数: $(nproc)"

# Anniversary视频 - 102MB → 80MB (2-Pass VBR)
compress_video_parallel "src/client/assets/videos/anniversary/anniversary.mp4" "anniversary.mp4" "80" "纪念日视频 - 2-Pass VBR 80MB"

# Together-days视频 - 146MB → 80MB (2-Pass VBR)
compress_video_parallel "src/client/assets/videos/together-days/together-days.mp4" "together-days.mp4" "80" "在一起的日子 - 2-Pass VBR 80MB"

# Memorial视频 - 93MB → 80MB (2-Pass VBR)
compress_video_parallel "src/client/assets/videos/memorial/memorial.mp4" "memorial.mp4" "80" "纪念相册 - 2-Pass VBR 80MB"

# Home视频 - 63MB (直接复制，小于90MB)
compress_video_parallel "src/client/assets/videos/home/<USER>" "home.mp4" "80" "首页视频 - 直接复制"

# Meetings视频 - 39MB (直接复制，小于90MB)
compress_video_parallel "src/client/assets/videos/meetings/meetings.mp4" "meetings.mp4" "80" "相遇回忆 - 直接复制"

# 洱海视频 - CRF 14 (高质量)
compress_video_parallel "src/client/assets/videos/洱海/DJI_0075.MP4" "erhai.mp4" "14" "洱海风光 - 高质量"

# 等待所有并行任务完成
wait_for_all_jobs

# 压缩结果统计
echo ""
echo "📊 H.265压缩结果汇总："
echo "========================================"

echo ""
echo "原始文件大小："
if [ -d "src/client/assets/videos" ]; then
    find src/client/assets/videos -name "*.mp4" -exec ls -lh {} \; | sort
else
    echo "  源文件目录不存在"
fi

echo ""
echo "压缩后文件大小："
if [ -d "src/client/assets/video-compressed" ]; then
    ls -lh src/client/assets/video-compressed/*.mp4 2>/dev/null | sort
else
    echo "  压缩文件目录为空"
fi

# 计算总体压缩效果
echo ""
echo "🎯 H.265并行2-Pass VBR压缩参数总结："
echo "  • 编码器: libx265 (H.265/HEVC)"
echo "  • 方法: 2-Pass VBR (Variable Bitrate)"
echo "  • 并行度: $MAX_PARALLEL_JOBS 个任务同时处理"
echo "  • CPU核心: $(nproc) 核心"
echo "  • 目标大小: 80MB (大文件压缩目标)"
echo "  • 压缩阈值: 90MB (大于此值才压缩)"
echo "  • 预设: slow (高质量压缩)"
echo "  • 音频: 无音频 (-an)"
echo "  • 像素格式: yuv420p (兼容性)"
echo "  • 智能策略: <90MB文件直接复制，≥90MB文件2-Pass VBR压缩至80MB"

echo ""
echo "✅ H.265并行2-Pass VBR压缩任务完成！"
echo "📁 统一存储路径: src/client/assets/video-compressed/"
echo "🚀 所有文件已准备好上传到四层CDN架构"
